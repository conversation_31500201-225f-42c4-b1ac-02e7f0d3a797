<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Video Manager - Upload & Manage Your Videos</title>
    <link href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.0.0/css/all.min.css" rel="stylesheet">
    <link href="assets/css/style.css" rel="stylesheet">
</head>
<body>
    <nav class="navbar">
        <div class="nav-container">
            <div class="nav-logo">
                <i class="fas fa-video"></i>
                <span>VideoManager</span>
            </div>
            <div class="nav-menu" id="navMenu">
                <a href="#home" class="nav-link">Home</a>
                <a href="#upload" class="nav-link">Upload</a>
                <a href="#library" class="nav-link">My Library</a>
                <a href="#playlists" class="nav-link">Playlists</a>
                <a href="#profile" class="nav-link">Profile</a>
            </div>
            <div class="nav-auth">
                <button class="btn btn-outline" id="loginBtn">Login</button>
                <button class="btn btn-primary" id="registerBtn">Register</button>
            </div>
            <div class="nav-toggle" id="navToggle">
                <span class="bar"></span>
                <span class="bar"></span>
                <span class="bar"></span>
            </div>
        </div>
    </nav>

    <main>
        <!-- Home Section -->
        <section id="home" class="hero">
            <div class="hero-content">
                <h1>Manage Your Video Content</h1>
                <p>Upload, organize, and share your videos securely. Convert formats, create playlists, and manage your personal video library.</p>
                <div class="hero-buttons">
                    <button class="btn btn-primary btn-large" onclick="showSection('upload')">
                        <i class="fas fa-upload"></i> Start Uploading
                    </button>
                    <button class="btn btn-outline btn-large" onclick="showSection('library')">
                        <i class="fas fa-folder"></i> Browse Library
                    </button>
                </div>
            </div>
        </section>

        <!-- Upload Section -->
        <section id="upload" class="section hidden">
            <div class="container">
                <h2><i class="fas fa-cloud-upload-alt"></i> Upload Videos</h2>
                
                <div class="upload-area" id="uploadArea">
                    <div class="upload-content">
                        <i class="fas fa-cloud-upload-alt upload-icon"></i>
                        <h3>Drag & Drop Videos Here</h3>
                        <p>Or click to select files</p>
                        <input type="file" id="fileInput" multiple accept="video/*" hidden>
                        <button class="btn btn-primary" onclick="document.getElementById('fileInput').click()">
                            Choose Files
                        </button>
                    </div>
                </div>

                <div class="upload-options">
                    <div class="form-group">
                        <label for="videoTitle">Title</label>
                        <input type="text" id="videoTitle" placeholder="Enter video title">
                    </div>
                    <div class="form-group">
                        <label for="videoDescription">Description</label>
                        <textarea id="videoDescription" placeholder="Enter video description"></textarea>
                    </div>
                    <div class="form-group">
                        <label for="videoCategory">Category</label>
                        <select id="videoCategory">
                            <option value="">Select Category</option>
                            <option value="1">Personal</option>
                            <option value="2">Educational</option>
                            <option value="3">Entertainment</option>
                            <option value="4">Business</option>
                            <option value="5">Creative</option>
                        </select>
                    </div>
                    <div class="form-group">
                        <label>
                            <input type="checkbox" id="isPublic"> Make this video public
                        </label>
                    </div>
                </div>

                <div class="upload-progress hidden" id="uploadProgress">
                    <div class="progress-bar">
                        <div class="progress-fill" id="progressFill"></div>
                    </div>
                    <div class="progress-text" id="progressText">0%</div>
                </div>
            </div>
        </section>

        <!-- Library Section -->
        <section id="library" class="section hidden">
            <div class="container">
                <div class="section-header">
                    <h2><i class="fas fa-folder-open"></i> My Video Library</h2>
                    <div class="library-controls">
                        <input type="search" id="searchVideos" placeholder="Search videos...">
                        <select id="filterCategory">
                            <option value="">All Categories</option>
                            <option value="1">Personal</option>
                            <option value="2">Educational</option>
                            <option value="3">Entertainment</option>
                            <option value="4">Business</option>
                            <option value="5">Creative</option>
                        </select>
                        <button class="btn btn-outline" id="viewToggle">
                            <i class="fas fa-th"></i>
                        </button>
                    </div>
                </div>
                
                <div class="video-grid" id="videoGrid">
                    <!-- Videos will be loaded here -->
                </div>
            </div>
        </section>

        <!-- Playlists Section -->
        <section id="playlists" class="section hidden">
            <div class="container">
                <div class="section-header">
                    <h2><i class="fas fa-list"></i> Playlists</h2>
                    <button class="btn btn-primary" id="createPlaylistBtn">
                        <i class="fas fa-plus"></i> Create Playlist
                    </button>
                </div>
                
                <div class="playlist-grid" id="playlistGrid">
                    <!-- Playlists will be loaded here -->
                </div>
            </div>
        </section>
    </main>

    <!-- Modals -->
    <div id="loginModal" class="modal hidden">
        <div class="modal-content">
            <span class="close">&times;</span>
            <h2>Login</h2>
            <form id="loginForm">
                <div class="form-group">
                    <label for="loginEmail">Email</label>
                    <input type="email" id="loginEmail" required>
                </div>
                <div class="form-group">
                    <label for="loginPassword">Password</label>
                    <input type="password" id="loginPassword" required>
                </div>
                <button type="submit" class="btn btn-primary btn-full">Login</button>
            </form>
        </div>
    </div>

    <div id="registerModal" class="modal hidden">
        <div class="modal-content">
            <span class="close">&times;</span>
            <h2>Register</h2>
            <form id="registerForm">
                <div class="form-group">
                    <label for="registerUsername">Username</label>
                    <input type="text" id="registerUsername" required>
                </div>
                <div class="form-group">
                    <label for="registerEmail">Email</label>
                    <input type="email" id="registerEmail" required>
                </div>
                <div class="form-group">
                    <label for="registerPassword">Password</label>
                    <input type="password" id="registerPassword" required>
                </div>
                <button type="submit" class="btn btn-primary btn-full">Register</button>
            </form>
        </div>
    </div>

    <script src="assets/js/app.js"></script>
</body>
</html>
