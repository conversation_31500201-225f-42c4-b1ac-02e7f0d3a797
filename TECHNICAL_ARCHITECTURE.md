# Video Downloader System - Technical Architecture

## ⚠️ LEGAL DISCLAIMER
**IMPORTANT**: This architecture is provided for educational purposes only. Downloading content from platforms like YouTube, Instagram, TikTok, Facebook, and Twitter may violate their Terms of Service and copyright laws. Always ensure you have proper authorization before downloading any content.

## System Architecture Overview

```
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Frontend      │    │   Backend API   │    │   External      │
│   (React/Vue)   │◄──►│   (Node.js/     │◄──►│   Services      │
│                 │    │    Python)      │    │   (yt-dlp, etc) │
└─────────────────┘    └─────────────────┘    └─────────────────┘
         │                       │                       │
         ▼                       ▼                       ▼
┌─────────────────┐    ┌─────────────────┐    ┌─────────────────┐
│   Browser       │    │   Database      │    │   File Storage  │
│   Extension     │    │   (PostgreSQL)  │    │   (Local/Cloud) │
└─────────────────┘    └─────────────────┘    └─────────────────┘
```

## Core Components

### 1. Frontend Layer
- **Web Interface**: React/Vue.js SPA
- **Desktop App**: Electron wrapper
- **Mobile PWA**: Progressive Web App
- **Browser Extension**: Chrome/Firefox add-on

### 2. Backend Services
- **API Gateway**: Express.js/FastAPI
- **Download Engine**: yt-dlp integration
- **Media Processor**: FFmpeg for conversion
- **Queue Manager**: Redis/Bull for job processing

### 3. Data Layer
- **Primary Database**: PostgreSQL
- **Cache Layer**: Redis
- **File Storage**: Local filesystem or cloud storage
- **Metadata Store**: Elasticsearch (optional)

## Recommended Tech Stack

### Backend Options

#### Option 1: Node.js Stack
```javascript
// Core dependencies
{
  "express": "^4.18.0",
  "yt-dlp-wrap": "^2.0.0",
  "fluent-ffmpeg": "^2.1.0",
  "bull": "^4.10.0",
  "redis": "^4.6.0",
  "pg": "^8.8.0",
  "multer": "^1.4.0",
  "helmet": "^6.0.0"
}
```

#### Option 2: Python Stack
```python
# requirements.txt
fastapi==0.95.0
yt-dlp==2023.3.4
celery==5.2.7
redis==4.5.4
psycopg2-binary==2.9.6
python-multipart==0.0.6
pydantic==1.10.7
```

### Frontend Stack
```json
{
  "react": "^18.2.0",
  "typescript": "^5.0.0",
  "tailwindcss": "^3.3.0",
  "axios": "^1.4.0",
  "react-query": "^3.39.0",
  "react-router-dom": "^6.10.0"
}
```

## Database Schema

### Core Tables
```sql
-- Users and authentication
CREATE TABLE users (
    id SERIAL PRIMARY KEY,
    username VARCHAR(50) UNIQUE NOT NULL,
    email VARCHAR(100) UNIQUE NOT NULL,
    password_hash VARCHAR(255) NOT NULL,
    created_at TIMESTAMP DEFAULT NOW()
);

-- Download requests
CREATE TABLE downloads (
    id SERIAL PRIMARY KEY,
    user_id INTEGER REFERENCES users(id),
    url TEXT NOT NULL,
    platform VARCHAR(50) NOT NULL,
    status VARCHAR(20) DEFAULT 'pending',
    title TEXT,
    description TEXT,
    thumbnail_url TEXT,
    file_path TEXT,
    file_size BIGINT,
    format VARCHAR(10),
    quality VARCHAR(20),
    created_at TIMESTAMP DEFAULT NOW(),
    completed_at TIMESTAMP
);

-- Download queue
CREATE TABLE download_queue (
    id SERIAL PRIMARY KEY,
    download_id INTEGER REFERENCES downloads(id),
    priority INTEGER DEFAULT 0,
    retry_count INTEGER DEFAULT 0,
    error_message TEXT,
    created_at TIMESTAMP DEFAULT NOW()
);

-- User preferences
CREATE TABLE user_preferences (
    user_id INTEGER PRIMARY KEY REFERENCES users(id),
    default_format VARCHAR(10) DEFAULT 'mp4',
    default_quality VARCHAR(20) DEFAULT '720p',
    download_path TEXT,
    auto_subtitle BOOLEAN DEFAULT false,
    watermark_removal BOOLEAN DEFAULT false
);
```

## API Endpoints

### Authentication
```
POST /api/auth/register
POST /api/auth/login
POST /api/auth/refresh
DELETE /api/auth/logout
```

### Download Management
```
POST /api/downloads          # Submit download request
GET /api/downloads           # List user downloads
GET /api/downloads/:id       # Get download details
DELETE /api/downloads/:id    # Cancel/delete download
POST /api/downloads/batch    # Batch download (playlists)
```

### Platform Support
```
GET /api/platforms           # List supported platforms
POST /api/platforms/validate # Validate URL
GET /api/platforms/info      # Get video info without downloading
```

## Security Implementation

### 1. Input Validation
```python
from pydantic import BaseModel, HttpUrl
from typing import Optional

class DownloadRequest(BaseModel):
    url: HttpUrl
    format: Optional[str] = "mp4"
    quality: Optional[str] = "720p"
    extract_audio: Optional[bool] = False
    
    class Config:
        # Prevent XSS and injection attacks
        validate_assignment = True
```

### 2. Rate Limiting
```javascript
const rateLimit = require('express-rate-limit');

const downloadLimiter = rateLimit({
  windowMs: 15 * 60 * 1000, // 15 minutes
  max: 10, // limit each IP to 10 downloads per windowMs
  message: 'Too many download requests'
});
```

### 3. File Scanning
```python
import hashlib
import requests

def scan_file_for_malware(file_path):
    # Integration with VirusTotal API
    with open(file_path, 'rb') as f:
        file_hash = hashlib.sha256(f.read()).hexdigest()
    
    # Check against known malware hashes
    response = requests.get(f'https://www.virustotal.com/vtapi/v2/file/report', 
                          params={'apikey': VT_API_KEY, 'resource': file_hash})
    return response.json()
```

## Download Engine Implementation

### Core Download Service
```python
import yt_dlp
from celery import Celery

app = Celery('downloader')

@app.task
def download_video(download_id, url, options):
    try:
        ydl_opts = {
            'format': options.get('format', 'best'),
            'outtmpl': f'downloads/{download_id}/%(title)s.%(ext)s',
            'writesubtitles': options.get('subtitles', False),
            'writeautomaticsub': options.get('auto_subtitles', False),
        }
        
        with yt_dlp.YoutubeDL(ydl_opts) as ydl:
            info = ydl.extract_info(url, download=True)
            
        # Update database with completion status
        update_download_status(download_id, 'completed', info)
        
    except Exception as e:
        update_download_status(download_id, 'failed', str(e))
```

### Platform-Specific Extractors
```python
class PlatformExtractor:
    @staticmethod
    def extract_youtube(url):
        return yt_dlp.YoutubeDL().extract_info(url, download=False)
    
    @staticmethod
    def extract_instagram(url):
        # Custom Instagram extractor with watermark removal
        opts = {
            'writesubtitles': False,
            'writeautomaticsub': False,
            'format': 'best[height<=1080]'
        }
        return yt_dlp.YoutubeDL(opts).extract_info(url, download=False)
    
    @staticmethod
    def extract_tiktok(url):
        # TikTok-specific options for watermark removal
        opts = {
            'format': 'best[ext=mp4]',
            'writesubtitles': False,
        }
        return yt_dlp.YoutubeDL(opts).extract_info(url, download=False)
```

## Browser Extension Architecture

### Manifest (Chrome Extension)
```json
{
  "manifest_version": 3,
  "name": "Video Downloader",
  "version": "1.0",
  "permissions": ["activeTab", "storage"],
  "host_permissions": [
    "https://www.youtube.com/*",
    "https://www.instagram.com/*",
    "https://www.tiktok.com/*"
  ],
  "content_scripts": [{
    "matches": ["<all_urls>"],
    "js": ["content.js"]
  }],
  "action": {
    "default_popup": "popup.html"
  }
}
```

### Content Script
```javascript
// content.js
function detectVideoUrls() {
  const currentUrl = window.location.href;
  const platform = detectPlatform(currentUrl);
  
  if (platform) {
    chrome.runtime.sendMessage({
      action: 'videoDetected',
      url: currentUrl,
      platform: platform,
      title: document.title
    });
  }
}

function detectPlatform(url) {
  if (url.includes('youtube.com') || url.includes('youtu.be')) return 'youtube';
  if (url.includes('instagram.com')) return 'instagram';
  if (url.includes('tiktok.com')) return 'tiktok';
  if (url.includes('facebook.com')) return 'facebook';
  if (url.includes('twitter.com') || url.includes('x.com')) return 'twitter';
  return null;
}

// Run detection on page load and URL changes
detectVideoUrls();
window.addEventListener('popstate', detectVideoUrls);
```

## Deployment Architecture

### Docker Configuration
```dockerfile
# Dockerfile
FROM node:18-alpine

# Install system dependencies
RUN apk add --no-cache python3 py3-pip ffmpeg

# Install yt-dlp
RUN pip3 install yt-dlp

WORKDIR /app
COPY package*.json ./
RUN npm install

COPY . .
EXPOSE 3000

CMD ["npm", "start"]
```

### Docker Compose
```yaml
version: '3.8'
services:
  app:
    build: .
    ports:
      - "3000:3000"
    environment:
      - DATABASE_URL=******************************/videodownloader
      - REDIS_URL=redis://redis:6379
    depends_on:
      - db
      - redis

  db:
    image: postgres:15
    environment:
      POSTGRES_DB: videodownloader
      POSTGRES_USER: user
      POSTGRES_PASSWORD: pass
    volumes:
      - postgres_data:/var/lib/postgresql/data

  redis:
    image: redis:7-alpine
    ports:
      - "6379:6379"

volumes:
  postgres_data:
```

## Legal and Ethical Considerations

### 1. Terms of Service Compliance
- **YouTube**: Prohibits downloading content without explicit permission
- **Instagram**: Terms forbid automated data collection
- **TikTok**: Restricts downloading of user content
- **Facebook**: Prohibits automated access to content
- **Twitter**: API terms restrict bulk downloading

### 2. Copyright Protection
```python
# Copyright disclaimer implementation
class CopyrightChecker:
    def __init__(self):
        self.disclaimer_shown = False
    
    def show_disclaimer(self):
        return """
        ⚠️ COPYRIGHT NOTICE ⚠️
        
        This tool is for personal use only. You are responsible for:
        - Ensuring you have permission to download content
        - Complying with platform Terms of Service
        - Respecting copyright and intellectual property rights
        - Using downloaded content in accordance with applicable laws
        
        By proceeding, you acknowledge these responsibilities.
        """
    
    def require_acknowledgment(self, user_id):
        # Require users to acknowledge copyright responsibilities
        pass
```

### 3. DMCA Compliance
```python
class DMCAHandler:
    def handle_takedown_request(self, request):
        # Process DMCA takedown requests
        # Remove content and notify users
        pass
    
    def implement_repeat_infringer_policy(self):
        # Suspend accounts with multiple violations
        pass
```

## Alternative Legal Implementations

### 1. Content Aggregation Service
Instead of downloading, create a bookmark/aggregation service:
```python
class ContentAggregator:
    def bookmark_video(self, url, user_id):
        # Save video metadata and link
        # Use official embed codes
        pass
    
    def create_playlist(self, video_urls, user_id):
        # Create playlists of bookmarked content
        pass
```

### 2. Educational Platform Integration
```python
class EducationalContent:
    def integrate_creative_commons(self):
        # Only allow CC-licensed content
        pass
    
    def verify_educational_use(self, user_id):
        # Verify legitimate educational purposes
        pass
```

This architecture provides a comprehensive foundation for a video management system while highlighting the legal complexities of video downloading from major platforms.
