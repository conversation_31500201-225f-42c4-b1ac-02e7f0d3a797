<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

require_once '../config/database.php';
require_once '../includes/jwt.php';

class UploadAPI {
    private $db;
    private $jwt;
    private $currentUser;

    public function __construct() {
        $database = new Database();
        $this->db = $database->getConnection();
        $this->jwt = new JWT();
        $this->authenticateUser();
    }

    private function authenticateUser() {
        $headers = getallheaders();
        $authHeader = isset($headers['Authorization']) ? $headers['Authorization'] : '';
        
        if (strpos($authHeader, 'Bearer ') !== 0) {
            $this->sendResponse(false, 'Authentication required');
            exit;
        }

        $token = substr($authHeader, 7);
        
        try {
            $decoded = $this->jwt->decode($token);
            
            if ($decoded['exp'] < time()) {
                $this->sendResponse(false, 'Token expired');
                exit;
            }

            $this->currentUser = $decoded;
        } catch (Exception $e) {
            $this->sendResponse(false, 'Invalid token');
            exit;
        }
    }

    public function handleUpload() {
        if ($_SERVER['REQUEST_METHOD'] !== 'POST') {
            $this->sendResponse(false, 'Method not allowed');
            return;
        }

        if (!isset($_FILES['video'])) {
            $this->sendResponse(false, 'No video file uploaded');
            return;
        }

        $file = $_FILES['video'];
        
        // Validate file
        if ($file['error'] !== UPLOAD_ERR_OK) {
            $this->sendResponse(false, 'Upload error: ' . $this->getUploadError($file['error']));
            return;
        }

        if ($file['size'] > MAX_FILE_SIZE) {
            $this->sendResponse(false, 'File size exceeds maximum allowed size');
            return;
        }

        $fileExtension = strtolower(pathinfo($file['name'], PATHINFO_EXTENSION));
        if (!in_array($fileExtension, ALLOWED_FORMATS)) {
            $this->sendResponse(false, 'Invalid file format');
            return;
        }

        // Generate unique filename
        $filename = uniqid() . '_' . time() . '.' . $fileExtension;
        $filePath = UPLOAD_PATH . $filename;

        // Move uploaded file
        if (!move_uploaded_file($file['tmp_name'], $filePath)) {
            $this->sendResponse(false, 'Failed to save file');
            return;
        }

        // Get video metadata
        $metadata = $this->getVideoMetadata($filePath);

        // Save to database
        try {
            $stmt = $this->db->prepare("
                INSERT INTO videos (user_id, title, description, filename, original_filename, 
                                  file_path, file_size, duration, format, resolution, category_id, is_public) 
                VALUES (?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?, ?)
            ");

            $stmt->execute([
                $this->currentUser['user_id'],
                $_POST['title'] ?? $file['name'],
                $_POST['description'] ?? '',
                $filename,
                $file['name'],
                $filePath,
                $file['size'],
                $metadata['duration'] ?? null,
                $fileExtension,
                $metadata['resolution'] ?? null,
                $_POST['category_id'] ?? null,
                $_POST['is_public'] ?? 0
            ]);

            $videoId = $this->db->lastInsertId();

            // Generate thumbnail
            $this->generateThumbnail($filePath, $videoId);

            $this->sendResponse(true, 'Video uploaded successfully', [
                'video_id' => $videoId
            ]);

        } catch (Exception $e) {
            // Clean up file if database insert fails
            if (file_exists($filePath)) {
                unlink($filePath);
            }
            $this->sendResponse(false, 'Database error: ' . $e->getMessage());
        }
    }

    private function getVideoMetadata($filePath) {
        $metadata = [];
        
        // Use FFmpeg to get video information if available
        if (function_exists('shell_exec')) {
            $ffmpegPath = 'ffmpeg'; // Adjust path as needed
            $command = "$ffmpegPath -i " . escapeshellarg($filePath) . " 2>&1";
            $output = shell_exec($command);
            
            if ($output) {
                // Extract duration
                if (preg_match('/Duration: (\d{2}):(\d{2}):(\d{2})/', $output, $matches)) {
                    $hours = intval($matches[1]);
                    $minutes = intval($matches[2]);
                    $seconds = intval($matches[3]);
                    $metadata['duration'] = $hours * 3600 + $minutes * 60 + $seconds;
                }
                
                // Extract resolution
                if (preg_match('/(\d{3,4})x(\d{3,4})/', $output, $matches)) {
                    $metadata['resolution'] = $matches[1] . 'x' . $matches[2];
                }
            }
        }
        
        return $metadata;
    }

    private function generateThumbnail($videoPath, $videoId) {
        if (!function_exists('shell_exec')) {
            return false;
        }

        $thumbnailPath = THUMBNAIL_PATH . $videoId . '.jpg';
        $ffmpegPath = 'ffmpeg'; // Adjust path as needed
        
        $command = "$ffmpegPath -i " . escapeshellarg($videoPath) . 
                   " -ss 00:00:01 -vframes 1 -y " . escapeshellarg($thumbnailPath) . " 2>&1";
        
        shell_exec($command);
        
        if (file_exists($thumbnailPath)) {
            // Update database with thumbnail path
            $stmt = $this->db->prepare("UPDATE videos SET thumbnail_path = ? WHERE id = ?");
            $stmt->execute([$thumbnailPath, $videoId]);
            return true;
        }
        
        return false;
    }

    private function getUploadError($errorCode) {
        switch ($errorCode) {
            case UPLOAD_ERR_INI_SIZE:
                return 'File exceeds upload_max_filesize';
            case UPLOAD_ERR_FORM_SIZE:
                return 'File exceeds MAX_FILE_SIZE';
            case UPLOAD_ERR_PARTIAL:
                return 'File was only partially uploaded';
            case UPLOAD_ERR_NO_FILE:
                return 'No file was uploaded';
            case UPLOAD_ERR_NO_TMP_DIR:
                return 'Missing temporary folder';
            case UPLOAD_ERR_CANT_WRITE:
                return 'Failed to write file to disk';
            case UPLOAD_ERR_EXTENSION:
                return 'Upload stopped by extension';
            default:
                return 'Unknown upload error';
        }
    }

    private function sendResponse($success, $message, $data = null) {
        $response = [
            'success' => $success,
            'message' => $message
        ];

        if ($data) {
            $response = array_merge($response, $data);
        }

        echo json_encode($response);
    }
}

$api = new UploadAPI();
$api->handleUpload();
?>
