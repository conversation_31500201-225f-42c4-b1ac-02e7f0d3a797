<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, DELETE, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

require_once '../config/database.php';
require_once '../includes/jwt.php';

class VideosAPI {
    private $db;
    private $jwt;
    private $currentUser;

    public function __construct() {
        $database = new Database();
        $this->db = $database->getConnection();
        $this->jwt = new JWT();
        $this->authenticateUser();
    }

    private function authenticateUser() {
        $headers = getallheaders();
        $authHeader = isset($headers['Authorization']) ? $headers['Authorization'] : '';
        
        if (strpos($authHeader, 'Bearer ') !== 0) {
            $this->sendResponse(false, 'Authentication required');
            exit;
        }

        $token = substr($authHeader, 7);
        
        try {
            $decoded = $this->jwt->decode($token);
            
            if ($decoded['exp'] < time()) {
                $this->sendResponse(false, 'Token expired');
                exit;
            }

            $this->currentUser = $decoded;
        } catch (Exception $e) {
            $this->sendResponse(false, 'Invalid token');
            exit;
        }
    }

    public function handleRequest() {
        $method = $_SERVER['REQUEST_METHOD'];
        
        switch ($method) {
            case 'GET':
                if (isset($_GET['id'])) {
                    $this->getVideo($_GET['id']);
                } else {
                    $this->getVideos();
                }
                break;
            case 'DELETE':
                $pathInfo = $_SERVER['PATH_INFO'] ?? '';
                $videoId = trim($pathInfo, '/');
                if ($videoId) {
                    $this->deleteVideo($videoId);
                } else {
                    $this->sendResponse(false, 'Video ID required');
                }
                break;
            default:
                $this->sendResponse(false, 'Method not allowed');
        }
    }

    private function getVideos() {
        try {
            $search = $_GET['search'] ?? '';
            $category = $_GET['category'] ?? '';
            $page = max(1, intval($_GET['page'] ?? 1));
            $limit = min(50, max(1, intval($_GET['limit'] ?? 20)));
            $offset = ($page - 1) * $limit;

            $whereClause = "WHERE user_id = ?";
            $params = [$this->currentUser['user_id']];

            if ($search) {
                $whereClause .= " AND (title LIKE ? OR description LIKE ?)";
                $searchTerm = "%$search%";
                $params[] = $searchTerm;
                $params[] = $searchTerm;
            }

            if ($category) {
                $whereClause .= " AND category_id = ?";
                $params[] = $category;
            }

            $stmt = $this->db->prepare("
                SELECT v.*, c.name as category_name 
                FROM videos v 
                LEFT JOIN categories c ON v.category_id = c.id 
                $whereClause 
                ORDER BY v.created_at DESC 
                LIMIT ? OFFSET ?
            ");

            $params[] = $limit;
            $params[] = $offset;
            $stmt->execute($params);
            $videos = $stmt->fetchAll();

            // Get total count
            $countStmt = $this->db->prepare("SELECT COUNT(*) FROM videos $whereClause");
            $countStmt->execute(array_slice($params, 0, -2));
            $totalCount = $countStmt->fetchColumn();

            $this->sendResponse(true, 'Videos retrieved successfully', [
                'videos' => $videos,
                'pagination' => [
                    'page' => $page,
                    'limit' => $limit,
                    'total' => $totalCount,
                    'pages' => ceil($totalCount / $limit)
                ]
            ]);

        } catch (Exception $e) {
            $this->sendResponse(false, 'Failed to retrieve videos: ' . $e->getMessage());
        }
    }

    private function getVideo($videoId) {
        try {
            $stmt = $this->db->prepare("
                SELECT v.*, c.name as category_name 
                FROM videos v 
                LEFT JOIN categories c ON v.category_id = c.id 
                WHERE v.id = ? AND v.user_id = ?
            ");
            $stmt->execute([$videoId, $this->currentUser['user_id']]);
            $video = $stmt->fetch();

            if (!$video) {
                $this->sendResponse(false, 'Video not found');
                return;
            }

            // Increment view count
            $updateStmt = $this->db->prepare("UPDATE videos SET download_count = download_count + 1 WHERE id = ?");
            $updateStmt->execute([$videoId]);

            $this->sendResponse(true, 'Video retrieved successfully', [
                'video' => $video
            ]);

        } catch (Exception $e) {
            $this->sendResponse(false, 'Failed to retrieve video: ' . $e->getMessage());
        }
    }

    private function deleteVideo($videoId) {
        try {
            // Get video info first
            $stmt = $this->db->prepare("SELECT * FROM videos WHERE id = ? AND user_id = ?");
            $stmt->execute([$videoId, $this->currentUser['user_id']]);
            $video = $stmt->fetch();

            if (!$video) {
                $this->sendResponse(false, 'Video not found');
                return;
            }

            // Delete from database
            $deleteStmt = $this->db->prepare("DELETE FROM videos WHERE id = ? AND user_id = ?");
            $deleteStmt->execute([$videoId, $this->currentUser['user_id']]);

            // Delete physical files
            if (file_exists($video['file_path'])) {
                unlink($video['file_path']);
            }
            if ($video['thumbnail_path'] && file_exists($video['thumbnail_path'])) {
                unlink($video['thumbnail_path']);
            }

            $this->sendResponse(true, 'Video deleted successfully');

        } catch (Exception $e) {
            $this->sendResponse(false, 'Failed to delete video: ' . $e->getMessage());
        }
    }

    private function sendResponse($success, $message, $data = null) {
        $response = [
            'success' => $success,
            'message' => $message
        ];

        if ($data) {
            $response = array_merge($response, $data);
        }

        echo json_encode($response);
    }
}

$api = new VideosAPI();
$api->handleRequest();
?>
