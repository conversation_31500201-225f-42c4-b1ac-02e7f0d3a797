<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: POST, GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type, Authorization');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

require_once '../config/database.php';
require_once '../includes/jwt.php';

class AuthAPI {
    private $db;
    private $jwt;

    public function __construct() {
        $database = new Database();
        $this->db = $database->getConnection();
        $this->jwt = new JWT();
    }

    public function handleRequest() {
        $method = $_SERVER['REQUEST_METHOD'];
        
        if ($method === 'POST') {
            $input = json_decode(file_get_contents('php://input'), true);
            
            if (!$input || !isset($input['action'])) {
                $this->sendResponse(false, 'Invalid request');
                return;
            }

            switch ($input['action']) {
                case 'login':
                    $this->login($input);
                    break;
                case 'register':
                    $this->register($input);
                    break;
                case 'verify':
                    $this->verifyToken();
                    break;
                default:
                    $this->sendResponse(false, 'Invalid action');
            }
        } else {
            $this->sendResponse(false, 'Method not allowed');
        }
    }

    private function login($data) {
        if (!isset($data['email']) || !isset($data['password'])) {
            $this->sendResponse(false, 'Email and password required');
            return;
        }

        try {
            $stmt = $this->db->prepare("SELECT id, username, email, password_hash FROM users WHERE email = ?");
            $stmt->execute([$data['email']]);
            $user = $stmt->fetch();

            if ($user && password_verify($data['password'], $user['password_hash'])) {
                $token = $this->jwt->encode([
                    'user_id' => $user['id'],
                    'email' => $user['email'],
                    'exp' => time() + (24 * 60 * 60) // 24 hours
                ]);

                $this->sendResponse(true, 'Login successful', [
                    'token' => $token,
                    'user' => [
                        'id' => $user['id'],
                        'username' => $user['username'],
                        'email' => $user['email']
                    ]
                ]);
            } else {
                $this->sendResponse(false, 'Invalid credentials');
            }
        } catch (Exception $e) {
            $this->sendResponse(false, 'Login failed: ' . $e->getMessage());
        }
    }

    private function register($data) {
        if (!isset($data['username']) || !isset($data['email']) || !isset($data['password'])) {
            $this->sendResponse(false, 'Username, email and password required');
            return;
        }

        // Validate input
        if (strlen($data['username']) < 3) {
            $this->sendResponse(false, 'Username must be at least 3 characters');
            return;
        }

        if (!filter_var($data['email'], FILTER_VALIDATE_EMAIL)) {
            $this->sendResponse(false, 'Invalid email format');
            return;
        }

        if (strlen($data['password']) < 6) {
            $this->sendResponse(false, 'Password must be at least 6 characters');
            return;
        }

        try {
            // Check if user already exists
            $stmt = $this->db->prepare("SELECT id FROM users WHERE email = ? OR username = ?");
            $stmt->execute([$data['email'], $data['username']]);
            
            if ($stmt->fetch()) {
                $this->sendResponse(false, 'User already exists');
                return;
            }

            // Create new user
            $passwordHash = password_hash($data['password'], PASSWORD_DEFAULT);
            $stmt = $this->db->prepare("INSERT INTO users (username, email, password_hash) VALUES (?, ?, ?)");
            $stmt->execute([$data['username'], $data['email'], $passwordHash]);

            $this->sendResponse(true, 'Registration successful');
        } catch (Exception $e) {
            $this->sendResponse(false, 'Registration failed: ' . $e->getMessage());
        }
    }

    private function verifyToken() {
        $headers = getallheaders();
        $authHeader = isset($headers['Authorization']) ? $headers['Authorization'] : '';
        
        if (strpos($authHeader, 'Bearer ') !== 0) {
            $this->sendResponse(false, 'Invalid token format');
            return;
        }

        $token = substr($authHeader, 7);
        
        try {
            $decoded = $this->jwt->decode($token);
            
            if ($decoded['exp'] < time()) {
                $this->sendResponse(false, 'Token expired');
                return;
            }

            // Get user info
            $stmt = $this->db->prepare("SELECT id, username, email FROM users WHERE id = ?");
            $stmt->execute([$decoded['user_id']]);
            $user = $stmt->fetch();

            if ($user) {
                $this->sendResponse(true, 'Token valid', [
                    'user' => $user
                ]);
            } else {
                $this->sendResponse(false, 'User not found');
            }
        } catch (Exception $e) {
            $this->sendResponse(false, 'Invalid token');
        }
    }

    private function sendResponse($success, $message, $data = null) {
        $response = [
            'success' => $success,
            'message' => $message
        ];

        if ($data) {
            $response = array_merge($response, $data);
        }

        echo json_encode($response);
    }
}

$api = new AuthAPI();
$api->handleRequest();
?>
