<?php
// Database configuration
class Database {
    private $host = 'localhost';
    private $db_name = 'video_manager';
    private $username = 'root';
    private $password = '';
    private $conn;

    public function getConnection() {
        $this->conn = null;
        
        try {
            $this->conn = new PDO(
                "mysql:host=" . $this->host . ";dbname=" . $this->db_name,
                $this->username,
                $this->password,
                array(
                    PDO::ATTR_ERRMODE => PDO::ERRMODE_EXCEPTION,
                    PDO::ATTR_DEFAULT_FETCH_MODE => PDO::FETCH_ASSOC,
                    PDO::MYSQL_ATTR_INIT_COMMAND => "SET NAMES utf8"
                )
            );
        } catch(PDOException $exception) {
            echo "Connection error: " . $exception->getMessage();
        }
        
        return $this->conn;
    }
}

// Application configuration
define('UPLOAD_PATH', 'uploads/videos/');
define('THUMBNAIL_PATH', 'uploads/thumbnails/');
define('MAX_FILE_SIZE', 500 * 1024 * 1024); // 500MB
define('ALLOWED_FORMATS', ['mp4', 'webm', 'avi', 'mov', 'mkv']);
define('JWT_SECRET', 'your-secret-key-change-this');
define('SITE_URL', 'http://localhost/videos');

// Create upload directories if they don't exist
if (!file_exists(UPLOAD_PATH)) {
    mkdir(UPLOAD_PATH, 0755, true);
}
if (!file_exists(THUMBNAIL_PATH)) {
    mkdir(THUMBNAIL_PATH, 0755, true);
}
?>
