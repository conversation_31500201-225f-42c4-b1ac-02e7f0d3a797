// Video Manager Application
class VideoManager {
    constructor() {
        this.currentUser = null;
        this.currentSection = 'home';
        this.init();
    }

    init() {
        this.setupEventListeners();
        this.checkAuthStatus();
        this.loadCategories();
    }

    setupEventListeners() {
        // Navigation
        document.querySelectorAll('.nav-link').forEach(link => {
            link.addEventListener('click', (e) => {
                e.preventDefault();
                const section = e.target.getAttribute('href').substring(1);
                this.showSection(section);
            });
        });

        // Auth buttons
        document.getElementById('loginBtn').addEventListener('click', () => {
            this.showModal('loginModal');
        });

        document.getElementById('registerBtn').addEventListener('click', () => {
            this.showModal('registerModal');
        });

        // Modal close buttons
        document.querySelectorAll('.close').forEach(closeBtn => {
            closeBtn.addEventListener('click', (e) => {
                this.hideModal(e.target.closest('.modal').id);
            });
        });

        // Forms
        document.getElementById('loginForm').addEventListener('submit', (e) => {
            e.preventDefault();
            this.handleLogin();
        });

        document.getElementById('registerForm').addEventListener('submit', (e) => {
            e.preventDefault();
            this.handleRegister();
        });

        // File upload
        const fileInput = document.getElementById('fileInput');
        const uploadArea = document.getElementById('uploadArea');

        fileInput.addEventListener('change', (e) => {
            this.handleFileSelect(e.target.files);
        });

        uploadArea.addEventListener('click', () => {
            fileInput.click();
        });

        uploadArea.addEventListener('dragover', (e) => {
            e.preventDefault();
            uploadArea.classList.add('dragover');
        });

        uploadArea.addEventListener('dragleave', () => {
            uploadArea.classList.remove('dragover');
        });

        uploadArea.addEventListener('drop', (e) => {
            e.preventDefault();
            uploadArea.classList.remove('dragover');
            this.handleFileSelect(e.dataTransfer.files);
        });

        // Search and filter
        document.getElementById('searchVideos').addEventListener('input', (e) => {
            this.searchVideos(e.target.value);
        });

        document.getElementById('filterCategory').addEventListener('change', (e) => {
            this.filterVideos(e.target.value);
        });
    }

    showSection(sectionName) {
        // Hide all sections
        document.querySelectorAll('.section, .hero').forEach(section => {
            section.classList.add('hidden');
        });

        // Show target section
        const targetSection = document.getElementById(sectionName);
        if (targetSection) {
            targetSection.classList.remove('hidden');
            this.currentSection = sectionName;

            // Load section-specific data
            if (sectionName === 'library') {
                this.loadVideos();
            } else if (sectionName === 'playlists') {
                this.loadPlaylists();
            }
        }
    }

    showModal(modalId) {
        document.getElementById(modalId).classList.remove('hidden');
    }

    hideModal(modalId) {
        document.getElementById(modalId).classList.add('hidden');
    }

    async handleLogin() {
        const email = document.getElementById('loginEmail').value;
        const password = document.getElementById('loginPassword').value;

        try {
            const response = await fetch('api/auth.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    action: 'login',
                    email: email,
                    password: password
                })
            });

            const result = await response.json();
            
            if (result.success) {
                this.currentUser = result.user;
                localStorage.setItem('token', result.token);
                this.updateAuthUI();
                this.hideModal('loginModal');
                this.showNotification('Login successful!', 'success');
            } else {
                this.showNotification(result.message, 'error');
            }
        } catch (error) {
            this.showNotification('Login failed. Please try again.', 'error');
        }
    }

    async handleRegister() {
        const username = document.getElementById('registerUsername').value;
        const email = document.getElementById('registerEmail').value;
        const password = document.getElementById('registerPassword').value;

        try {
            const response = await fetch('api/auth.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                },
                body: JSON.stringify({
                    action: 'register',
                    username: username,
                    email: email,
                    password: password
                })
            });

            const result = await response.json();
            
            if (result.success) {
                this.hideModal('registerModal');
                this.showNotification('Registration successful! Please login.', 'success');
            } else {
                this.showNotification(result.message, 'error');
            }
        } catch (error) {
            this.showNotification('Registration failed. Please try again.', 'error');
        }
    }

    handleFileSelect(files) {
        if (files.length === 0) return;

        const file = files[0];
        const maxSize = 500 * 1024 * 1024; // 500MB
        const allowedTypes = ['video/mp4', 'video/webm', 'video/avi', 'video/mov', 'video/quicktime'];

        if (file.size > maxSize) {
            this.showNotification('File size must be less than 500MB', 'error');
            return;
        }

        if (!allowedTypes.includes(file.type)) {
            this.showNotification('Please select a valid video file', 'error');
            return;
        }

        this.uploadVideo(file);
    }

    async uploadVideo(file) {
        const formData = new FormData();
        formData.append('video', file);
        formData.append('title', document.getElementById('videoTitle').value || file.name);
        formData.append('description', document.getElementById('videoDescription').value);
        formData.append('category_id', document.getElementById('videoCategory').value);
        formData.append('is_public', document.getElementById('isPublic').checked ? 1 : 0);

        const progressContainer = document.getElementById('uploadProgress');
        const progressFill = document.getElementById('progressFill');
        const progressText = document.getElementById('progressText');

        progressContainer.classList.remove('hidden');

        try {
            const xhr = new XMLHttpRequest();
            
            xhr.upload.addEventListener('progress', (e) => {
                if (e.lengthComputable) {
                    const percentComplete = (e.loaded / e.total) * 100;
                    progressFill.style.width = percentComplete + '%';
                    progressText.textContent = Math.round(percentComplete) + '%';
                }
            });

            xhr.addEventListener('load', () => {
                if (xhr.status === 200) {
                    const result = JSON.parse(xhr.responseText);
                    if (result.success) {
                        this.showNotification('Video uploaded successfully!', 'success');
                        this.resetUploadForm();
                        if (this.currentSection === 'library') {
                            this.loadVideos();
                        }
                    } else {
                        this.showNotification(result.message, 'error');
                    }
                } else {
                    this.showNotification('Upload failed. Please try again.', 'error');
                }
                progressContainer.classList.add('hidden');
            });

            xhr.open('POST', 'api/upload.php');
            xhr.setRequestHeader('Authorization', 'Bearer ' + localStorage.getItem('token'));
            xhr.send(formData);

        } catch (error) {
            this.showNotification('Upload failed. Please try again.', 'error');
            progressContainer.classList.add('hidden');
        }
    }

    resetUploadForm() {
        document.getElementById('videoTitle').value = '';
        document.getElementById('videoDescription').value = '';
        document.getElementById('videoCategory').value = '';
        document.getElementById('isPublic').checked = false;
        document.getElementById('fileInput').value = '';
    }

    showNotification(message, type) {
        // Create notification element
        const notification = document.createElement('div');
        notification.className = `notification ${type}`;
        notification.textContent = message;

        // Add to page
        document.body.appendChild(notification);

        // Remove after 3 seconds
        setTimeout(() => {
            notification.remove();
        }, 3000);
    }

    checkAuthStatus() {
        const token = localStorage.getItem('token');
        if (token) {
            // Verify token with server
            this.verifyToken(token);
        }
    }

    async verifyToken(token) {
        try {
            const response = await fetch('api/auth.php', {
                method: 'POST',
                headers: {
                    'Content-Type': 'application/json',
                    'Authorization': 'Bearer ' + token
                },
                body: JSON.stringify({
                    action: 'verify'
                })
            });

            const result = await response.json();

            if (result.success) {
                this.currentUser = result.user;
                this.updateAuthUI();
            } else {
                localStorage.removeItem('token');
            }
        } catch (error) {
            localStorage.removeItem('token');
        }
    }

    async loadCategories() {
        try {
            const response = await fetch('api/categories.php');
            const result = await response.json();

            if (result.success) {
                const categorySelect = document.getElementById('videoCategory');
                const filterSelect = document.getElementById('filterCategory');

                result.categories.forEach(category => {
                    const option = document.createElement('option');
                    option.value = category.id;
                    option.textContent = category.name;

                    categorySelect.appendChild(option.cloneNode(true));
                    filterSelect.appendChild(option);
                });
            }
        } catch (error) {
            console.error('Failed to load categories:', error);
        }
    }

    async loadVideos() {
        if (!this.currentUser) {
            this.showNotification('Please login to view your videos', 'error');
            return;
        }

        try {
            const response = await fetch('api/videos.php', {
                headers: {
                    'Authorization': 'Bearer ' + localStorage.getItem('token')
                }
            });

            const result = await response.json();

            if (result.success) {
                this.displayVideos(result.videos);
            } else {
                this.showNotification(result.message, 'error');
            }
        } catch (error) {
            this.showNotification('Failed to load videos', 'error');
        }
    }

    displayVideos(videos) {
        const videoGrid = document.getElementById('videoGrid');
        videoGrid.innerHTML = '';

        if (videos.length === 0) {
            videoGrid.innerHTML = '<p class="no-videos">No videos found. Upload your first video!</p>';
            return;
        }

        videos.forEach(video => {
            const videoCard = document.createElement('div');
            videoCard.className = 'video-card';
            videoCard.innerHTML = `
                <div class="video-thumbnail">
                    ${video.thumbnail_path ?
                        `<img src="${video.thumbnail_path}" alt="${video.title}">` :
                        '<i class="fas fa-video"></i>'
                    }
                </div>
                <div class="video-info">
                    <div class="video-title">${video.title}</div>
                    <div class="video-meta">
                        <span>${video.format?.toUpperCase()}</span>
                        <span>${this.formatFileSize(video.file_size)}</span>
                        <span>${this.formatDate(video.created_at)}</span>
                    </div>
                    <div class="video-actions">
                        <button class="btn btn-primary btn-sm" onclick="videoManager.playVideo(${video.id})">
                            <i class="fas fa-play"></i> Play
                        </button>
                        <button class="btn btn-outline btn-sm" onclick="videoManager.downloadVideo(${video.id})">
                            <i class="fas fa-download"></i> Download
                        </button>
                        <button class="btn btn-outline btn-sm" onclick="videoManager.deleteVideo(${video.id})">
                            <i class="fas fa-trash"></i> Delete
                        </button>
                    </div>
                </div>
            `;
            videoGrid.appendChild(videoCard);
        });
    }

    formatFileSize(bytes) {
        if (!bytes) return 'Unknown';
        const sizes = ['Bytes', 'KB', 'MB', 'GB'];
        const i = Math.floor(Math.log(bytes) / Math.log(1024));
        return Math.round(bytes / Math.pow(1024, i) * 100) / 100 + ' ' + sizes[i];
    }

    formatDate(dateString) {
        const date = new Date(dateString);
        return date.toLocaleDateString();
    }

    updateAuthUI() {
        const authDiv = document.querySelector('.nav-auth');
        if (this.currentUser) {
            authDiv.innerHTML = `
                <span>Welcome, ${this.currentUser.username}</span>
                <button class="btn btn-outline" onclick="videoManager.logout()">Logout</button>
            `;
        } else {
            authDiv.innerHTML = `
                <button class="btn btn-outline" id="loginBtn">Login</button>
                <button class="btn btn-primary" id="registerBtn">Register</button>
            `;
            // Re-attach event listeners
            document.getElementById('loginBtn').addEventListener('click', () => {
                this.showModal('loginModal');
            });
            document.getElementById('registerBtn').addEventListener('click', () => {
                this.showModal('registerModal');
            });
        }
    }

    logout() {
        localStorage.removeItem('token');
        this.currentUser = null;
        this.updateAuthUI();
        this.showSection('home');
        this.showNotification('Logged out successfully', 'success');
    }
}

// Initialize the application
const videoManager = new VideoManager();

// Global functions for HTML onclick events
function showSection(section) {
    videoManager.showSection(section);
}
