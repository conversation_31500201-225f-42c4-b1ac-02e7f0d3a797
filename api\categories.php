<?php
header('Content-Type: application/json');
header('Access-Control-Allow-Origin: *');
header('Access-Control-Allow-Methods: GET, OPTIONS');
header('Access-Control-Allow-Headers: Content-Type');

if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
    exit(0);
}

require_once '../config/database.php';

class CategoriesAPI {
    private $db;

    public function __construct() {
        $database = new Database();
        $this->db = $database->getConnection();
    }

    public function getCategories() {
        try {
            $stmt = $this->db->prepare("SELECT id, name, description FROM categories ORDER BY name");
            $stmt->execute();
            $categories = $stmt->fetchAll();

            $this->sendResponse(true, 'Categories retrieved successfully', [
                'categories' => $categories
            ]);

        } catch (Exception $e) {
            $this->sendResponse(false, 'Failed to retrieve categories: ' . $e->getMessage());
        }
    }

    private function sendResponse($success, $message, $data = null) {
        $response = [
            'success' => $success,
            'message' => $message
        ];

        if ($data) {
            $response = array_merge($response, $data);
        }

        echo json_encode($response);
    }
}

$api = new CategoriesAPI();
$api->getCategories();
?>
